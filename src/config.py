"""
VuManChu Cipher B Configuration Manager
Loads runtime configuration from environment variables
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """
    Configuration manager for VuManChu Cipher B application
    Handles runtime configuration only - algorithm parameters stay in parameters.py
    """

    # =============================================================================
    # API CREDENTIALS
    # =============================================================================
    BINANCE_API_KEY: Optional[str] = os.getenv('BINANCE_API_KEY')
    BINANCE_API_SECRET: Optional[str] = os.getenv('BINANCE_API_SECRET')

    # =============================================================================
    # APPLICATION SETTINGS
    # =============================================================================
    DEFAULT_SYMBOL: str = os.getenv('DEFAULT_SYMBOL', 'BTCUSDT')
    DEFAULT_INTERVAL: str = os.getenv('DEFAULT_INTERVAL', '4h')
    DEFAULT_LOOKBACK_DAYS: int = int(os.getenv('DEFAULT_LOOKBACK_DAYS', '100'))

    # =============================================================================
    # LOGGING CONFIGURATION
    # =============================================================================
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE: Optional[str] = os.getenv('LOG_FILE') or None

    # =============================================================================
    # DEVELOPMENT SETTINGS
    # =============================================================================
    DEBUG_MODE: bool = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
    TEST_MODE: bool = os.getenv('TEST_MODE', 'false').lower() == 'true'

    # =============================================================================
    # PERFORMANCE & SECURITY
    # =============================================================================
    API_TIMEOUT: int = int(os.getenv('API_TIMEOUT', '10'))
    API_RATE_LIMIT: int = int(os.getenv('API_RATE_LIMIT', '1200'))
    MAX_CANDLES_BUFFER: int = int(os.getenv('MAX_CANDLES_BUFFER', '1000'))
    CALCULATION_TIMEOUT: int = int(os.getenv('CALCULATION_TIMEOUT', '30'))

    # =============================================================================
    # OUTPUT SETTINGS
    # =============================================================================
    RESULTS_OUTPUT_FILE: str = os.getenv('RESULTS_OUTPUT_FILE', 'vumanchu_results.json')
    CHART_OUTPUT_FILE: str = os.getenv('CHART_OUTPUT_FILE', 'vumanchu_chart.html')
    CHART_WIDTH: int = int(os.getenv('CHART_WIDTH', '1200'))
    CHART_HEIGHT: int = int(os.getenv('CHART_HEIGHT', '800'))
    CHART_THEME: str = os.getenv('CHART_THEME', 'dark')
    
    @classmethod
    def get_binance_credentials(cls) -> tuple[Optional[str], Optional[str]]:
        """Get Binance API credentials"""
        return cls.BINANCE_API_KEY, cls.BINANCE_API_SECRET

    @classmethod
    def has_binance_credentials(cls) -> bool:
        """Check if Binance API credentials are configured"""
        return bool(cls.BINANCE_API_KEY and cls.BINANCE_API_SECRET)

    @classmethod
    def get_trading_defaults(cls) -> dict:
        """Get default trading configuration"""
        return {
            'symbol': cls.DEFAULT_SYMBOL,
            'interval': cls.DEFAULT_INTERVAL,
            'lookback_days': cls.DEFAULT_LOOKBACK_DAYS
        }

    @classmethod
    def get_log_config(cls) -> dict:
        """Get logging configuration"""
        config = {'level': cls.LOG_LEVEL}
        if cls.LOG_FILE:
            config['filename'] = cls.LOG_FILE
        return config


# Create a global config instance
config = Config()
