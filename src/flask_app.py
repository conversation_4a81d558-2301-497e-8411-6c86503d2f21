"""
VuManChu Cipher B Real-time Flask Web Application
Provides real-time lightweight charts with all indicators
"""

import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional

import numpy as np
import pandas as pd
from flask import Flask, render_template, request
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit

try:
    from .parameters import VuManChuParams, DEFAULT_PARAMS
    from .data_manager import DataManager
    from .indicators import generate_signals
    from .visualization import ChartVisualizer
    from .main import VuManChuCipherB
except ImportError:
    from parameters import VuManChuParams, DEFAULT_PARAMS
    from data_manager import DataManager
    from indicators import generate_signals
    from visualization import ChartVisualizer
    from main import VuManChuCipherB

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Flask app setup
app = Flask(__name__)
app.config['SECRET_KEY'] = 'vumanchu_cipher_b_secret_key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Import config for defaults
try:
    # Try relative import first (when run as module)
    from .config import config
except ImportError:
    # Fall back to direct import (when run as script)
    from config import config

# Global application state
vumanchu_app = None
data_thread = None
is_running = False
current_symbol = config.DEFAULT_SYMBOL
current_interval = config.DEFAULT_INTERVAL


class RealTimeChartApp:
    """
    Real-time chart application with continuous indicator updates
    """
    
    def __init__(self, symbol: str = None, interval: str = None):
        """
        Initialize real-time chart application

        Args:
            symbol: Trading pair symbol (uses config default if None)
            interval: Timeframe (uses config default if None)
        """
        # Use config defaults if parameters are None
        self.symbol = symbol if symbol is not None else config.DEFAULT_SYMBOL
        self.interval = interval if interval is not None else config.DEFAULT_INTERVAL
        self.vumanchu = VuManChuCipherB()
        self.is_running = False
        self.update_interval = 30  # seconds
        
        # Initialize with historical data
        self._initialize_data()
    
    def _initialize_data(self):
        """Initialize with historical data"""
        try:
            logger.info(f"Initializing data for {self.symbol} ({self.interval})")
            
            # Load historical data
            success = self.vumanchu.load_historical_data(
                self.symbol, self.interval, lookback_days=config.DEFAULT_LOOKBACK_DAYS
            )
            
            if success:
                # Calculate initial indicators
                self.vumanchu.calculate_indicators()
                logger.info("Initial data loaded successfully")
            else:
                logger.error("Failed to load initial data")
                
        except Exception as e:
            logger.error(f"Error initializing data: {e}")
    
    def get_chart_data(self) -> Dict[str, Any]:
        """
        Get current chart data for visualization
        
        Returns:
            Chart data structure
        """
        try:
            chart_data = self.vumanchu.create_chart_data()
            
            # Add metadata
            chart_data['symbol'] = self.symbol
            chart_data['interval'] = self.interval
            chart_data['timestamp'] = datetime.now().isoformat()
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error getting chart data: {e}")
            return {}
    
    def get_latest_signals(self) -> Dict[str, Any]:
        """
        Get latest trading signals
        
        Returns:
            Latest signal information
        """
        try:
            signals = self.vumanchu.get_latest_signals()
            signals['symbol'] = self.symbol
            signals['interval'] = self.interval
            return signals
            
        except Exception as e:
            logger.error(f"Error getting latest signals: {e}")
            return {}
    
    def update_data(self):
        """Update data with latest candle"""
        try:
            # Fetch sufficient historical data for indicator calculation
            # Use at least 30 days to ensure indicators have enough data
            success = self.vumanchu.load_historical_data(
                self.symbol, self.interval, lookback_days=30
            )
            
            if success:
                # Recalculate indicators
                self.vumanchu.calculate_indicators()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            return False
    
    def start_real_time_updates(self):
        """Start real-time data updates"""
        self.is_running = True
        logger.info("Starting real-time updates")
        
        while self.is_running:
            try:
                # Update data
                if self.update_data():
                    # Get updated chart data
                    chart_data = self.get_chart_data()
                    signals = self.get_latest_signals()
                    
                    # Emit updates to all connected clients
                    socketio.emit('chart_update', {
                        'chart_data': chart_data,
                        'signals': signals,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    logger.info(f"Chart update sent at {datetime.now()}")
                
                # Wait for next update
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in real-time update loop: {e}")
                time.sleep(5)  # Wait before retrying
    
    def stop_real_time_updates(self):
        """Stop real-time data updates"""
        self.is_running = False
        logger.info("Stopping real-time updates")


@app.route('/')
def index():
    """Main chart page"""
    return render_template('chart.html', 
                         symbol=current_symbol, 
                         interval=current_interval)


@app.route('/api/chart-data')
def get_chart_data():
    """API endpoint to get current chart data"""
    global vumanchu_app
    
    if vumanchu_app:
        chart_data = vumanchu_app.get_chart_data()
        return json.dumps(chart_data, default=str)
    
    return json.dumps({})


@app.route('/api/signals')
def get_signals():
    """API endpoint to get latest signals"""
    global vumanchu_app
    
    if vumanchu_app:
        signals = vumanchu_app.get_latest_signals()
        return json.dumps(signals, default=str)
    
    return json.dumps({})


@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info(f"Client connected: {request.sid}")

    # Send initial chart data
    global vumanchu_app
    if vumanchu_app:
        try:
            logger.info("Getting chart data for new client...")
            chart_data = vumanchu_app.get_chart_data()
            signals = vumanchu_app.get_latest_signals()

            logger.info(f"Chart data keys: {list(chart_data.keys()) if chart_data else 'None'}")
            if chart_data and 'candlestick_data' in chart_data:
                logger.info(f"Candlestick data length: {len(chart_data['candlestick_data'])}")
            if chart_data and 'wavetrend_series' in chart_data:
                logger.info(f"Wavetrend series length: {len(chart_data['wavetrend_series'])}")
            logger.info(f"Signals: {signals}")

            emit('initial_data', {
                'chart_data': chart_data,
                'signals': signals,
                'timestamp': datetime.now().isoformat()
            })
            logger.info("Initial data sent to client")
        except Exception as e:
            logger.error(f"Error sending initial data: {e}")
    else:
        logger.warning("VuManChu app not initialized")


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info(f"Client disconnected: {request.sid}")


@socketio.on('change_symbol')
def handle_symbol_change(data):
    """Handle symbol change request"""
    global vumanchu_app, current_symbol, current_interval
    
    try:
        new_symbol = data.get('symbol', config.DEFAULT_SYMBOL)
        new_interval = data.get('interval', config.DEFAULT_INTERVAL)
        
        logger.info(f"Changing symbol to {new_symbol} ({new_interval})")
        
        # Stop current updates
        if vumanchu_app:
            vumanchu_app.stop_real_time_updates()
        
        # Update global state
        current_symbol = new_symbol
        current_interval = new_interval
        
        # Create new app instance
        vumanchu_app = RealTimeChartApp(new_symbol, new_interval)
        
        # Send updated data
        chart_data = vumanchu_app.get_chart_data()
        signals = vumanchu_app.get_latest_signals()
        
        emit('symbol_changed', {
            'chart_data': chart_data,
            'signals': signals,
            'symbol': new_symbol,
            'interval': new_interval,
            'timestamp': datetime.now().isoformat()
        })
        
        # Restart updates in background
        start_background_updates()
        
    except Exception as e:
        logger.error(f"Error changing symbol: {e}")
        emit('error', {'message': str(e)})


def start_background_updates():
    """Start background data updates"""
    global data_thread, vumanchu_app, is_running
    
    if is_running and data_thread and data_thread.is_alive():
        return
    
    if vumanchu_app:
        is_running = True
        data_thread = threading.Thread(target=vumanchu_app.start_real_time_updates)
        data_thread.daemon = True
        data_thread.start()
        logger.info("Background updates started")


def initialize_app():
    """Initialize the application"""
    global vumanchu_app
    
    try:
        logger.info("Initializing VuManChu Flask application")
        
        # Create real-time chart app
        vumanchu_app = RealTimeChartApp(current_symbol, current_interval)
        
        logger.info("Application initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing application: {e}")


if __name__ == '__main__':
    # Initialize application
    initialize_app()
    
    # Start background updates
    start_background_updates()
    
    # Run Flask app
    logger.info("Starting Flask application on http://localhost:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
